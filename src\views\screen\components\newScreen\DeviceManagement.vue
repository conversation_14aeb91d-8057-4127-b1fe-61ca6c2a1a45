<template>
  <div class="device-management-page">
    <!-- 页面主体内容 -->
    <div class="page-content">
      <!-- 左侧：设备统计和列表 -->
      <div class="left-panel">
        <!-- 设备统计信息 - 科技风格 -->
        <div class="device-stats-dashboard">
          <div class="stats-header">
            <h3><i class="fas fa-chart-bar"></i> 设备监控概览</h3>
            <div class="refresh-btn" @click="fetchDeviceData">
              <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
            </div>
          </div>
          <div class="stats-grid">
            <div class="stat-card total">
              <div class="stat-icon">
                <i class="fas fa-server"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ deviceStats.total }}</div>
                <div class="stat-label">设备总数</div>
                <div class="stat-trend">
                  <i class="fas fa-arrow-up"></i>
                  <span>+2.5%</span>
                </div>
              </div>
            </div>
            <div class="stat-card online">
              <div class="stat-icon">
                <i class="fas fa-wifi"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ deviceStats.online }}</div>
                <div class="stat-label">在线设备</div>
                <div class="stat-progress">
                  <div class="progress-bar" :style="{ width: onlinePercentage + '%' }"></div>
                </div>
              </div>
            </div>
            <div class="stat-card offline">
              <div class="stat-icon">
                <i class="fas fa-exclamation-triangle"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ deviceStats.offline }}</div>
                <div class="stat-label">离线设备</div>
                <div class="stat-alert" v-if="deviceStats.offline > 0">
                  <i class="fas fa-bell"></i>
                  <span>需要关注</span>
                </div>
              </div>
            </div>
            <div class="stat-card sensors">
              <div class="stat-icon">
                <i class="fas fa-microchip"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ deviceStats.sensors }}</div>
                <div class="stat-label">传感器总数</div>
                <div class="stat-detail">
                  <span>活跃: {{ activeSensors }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 设备列表 -->
        <div class="device-list-section">
          <div class="section-header">
            <h3><i class="fas fa-list"></i> 设备列表</h3>
            <div class="header-controls">
              <div class="search-box">
                <i class="fas fa-search"></i>
                <input
                  type="text"
                  v-model="deviceSearchText"
                  placeholder="搜索设备..."
                  class="search-input"
                >
              </div>
              <div class="device-count">共 {{ filteredDeviceList.length }} 台设备</div>
            </div>
          </div>

          <div v-if="loading" class="loading-container">
            <div class="loading-spinner">
              <i class="fas fa-spinner fa-spin"></i>
            </div>
            <span>加载设备数据中...</span>
          </div>

          <div v-else-if="filteredDeviceList.length === 0" class="no-data-container">
            <i class="fas fa-database"></i>
            <p>{{ deviceList.length === 0 ? '暂无设备数据' : '没有符合搜索条件的设备' }}</p>
          </div>

          <div v-else class="device-list">
            <div
              v-for="device in filteredDeviceList"
              :key="device.id"
              class="device-item"
              @click="selectDevice(device)"
              :class="{ 'selected': selectedDevice && selectedDevice.id === device.id }"
            >
              <div class="device-item-header">
                <div class="device-name">
                  <i class="fas fa-microchip"></i>
                  {{ device.device_name || device.name || '未命名设备' }}
                </div>
                <div class="device-indicators">
                  <div class="health-indicator" :class="getDeviceHealthClass(device)" :title="getDeviceHealthText(device)">
                    <i class="fas fa-heart"></i>
                    <span>{{ getDeviceHealthScore(device) }}%</span>
                  </div>
                  <div class="device-status" :class="getDeviceStatusClass(device.status)">
                    <i :class="getDeviceStatusIcon(device.status)"></i>
                  </div>
                </div>
              </div>
              <div class="device-item-body">
                <div class="device-info">
                  <div class="info-row">
                    <span class="label">编号:</span>
                    <span class="value">{{ device.device_code || device.code || '-' }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">类型:</span>
                    <span class="value">{{ device.device_type || device.type || '-' }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">网关:</span>
                    <span class="value">{{ device.gateways ? device.gateways.length : 0 }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">传感器:</span>
                    <span class="value">{{ getDeviceSensorCount(device) }}</span>
                  </div>
                </div>
                <div class="device-health-bar">
                  <div class="health-bar-bg">
                    <div class="health-bar-fill" :style="{ width: getDeviceHealthScore(device) + '%' }" :class="getDeviceHealthClass(device)"></div>
                  </div>
                  <span class="health-text">设备健康度</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中央：3D模型展示区域 -->
      <div class="center-panel">
        <div class="model-container">
          <div class="model-header">
            <h3><i class="fas fa-cube"></i> 3D设备模型</h3>
            <div class="model-controls">
              <button class="control-btn" @click="resetModelView" title="重置视角">
                <i class="fas fa-home"></i>
              </button>
              <button class="control-btn" @click="toggleAutoRotate" title="自动旋转">
                <i class="fas fa-sync-alt" :class="{ 'active': autoRotate }"></i>
              </button>
              <button class="control-btn" @click="toggleSensorMarkers" title="显示/隐藏传感器标记">
                <i class="fas fa-map-marker-alt" :class="{ 'active': showSensorMarkers }"></i>
              </button>
              <button class="control-btn" @click="toggleFullscreen" title="全屏模式">
                <i class="fas fa-expand" :class="{ 'fa-compress': isFullscreen }"></i>
              </button>
              <button class="control-btn" @click="exportModelData" title="导出数据">
                <i class="fas fa-download"></i>
              </button>
            </div>
          </div>

          <!-- 3D模型显示区域 -->
          <div class="model-viewer" ref="modelContainer">
            <!-- 加载状态 -->
            <div v-if="modelLoading" class="model-loading">
              <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
              </div>
              <div class="loading-text">{{ loadingText }}</div>
              <div class="loading-progress" v-if="loadingProgress > 0">
                <div class="progress-bar" :style="{ width: loadingProgress + '%' }"></div>
                <span class="progress-text">{{ loadingProgress }}%</span>
              </div>
            </div>

            <!-- 无设备选择时的提示 -->
            <div v-else-if="!selectedDevice" class="no-device-selected">
              <div class="no-device-icon">
                <i class="fas fa-cube"></i>
              </div>
              <h3>选择设备查看3D模型</h3>
              <p>请从左侧设备列表中选择一个设备，系统将自动加载对应的3D模型进行展示</p>
              <div class="model-features">
                <div class="feature-item">
                  <i class="fas fa-eye"></i>
                  <span>实时3D可视化</span>
                </div>
                <div class="feature-item">
                  <i class="fas fa-map-marker-alt"></i>
                  <span>传感器位置标记</span>
                </div>
                <div class="feature-item">
                  <i class="fas fa-mouse"></i>
                  <span>交互式操作</span>
                </div>
              </div>
            </div>

            <!-- 传感器信息悬浮窗 -->
            <div v-if="hoveredSensor" class="sensor-tooltip" :style="tooltipStyle">
              <div class="tooltip-header">
                <i :class="getSensorIcon(hoveredSensor.sensor_type)"></i>
                <span>{{ hoveredSensor.sensor_name || '未命名传感器' }}</span>
              </div>
              <div class="tooltip-content">
                <div class="tooltip-row">
                  <span class="label">类型:</span>
                  <span class="value">{{ getSensorTypeText(hoveredSensor.sensor_type) }}</span>
                </div>
                <div class="tooltip-row">
                  <span class="label">数值:</span>
                  <span class="value">{{ formatSensorValue(hoveredSensor.sensor_value || hoveredSensor.current_value) }} {{ hoveredSensor.unit || '' }}</span>
                </div>
                <div class="tooltip-row">
                  <span class="label">状态:</span>
                  <span class="value" :class="getSensorStatusClass(hoveredSensor.status)">
                    {{ hoveredSensor.status === 1 ? '在线' : '离线' }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 模型信息面板 -->
          <div class="model-info-panel">
            <div class="info-item">
              <span class="label">选中设备:</span>
              <span class="value">{{ selectedDevice ? (selectedDevice.device_name || selectedDevice.name || '未命名设备') : '无' }}</span>
            </div>
            <div class="info-item" v-if="selectedDevice">
              <span class="label">传感器数量:</span>
              <span class="value">{{ sensorList.length }}</span>
            </div>
            <div class="info-item" v-if="selectedDevice">
              <span class="label">在线传感器:</span>
              <span class="value">{{ onlineSensorCount }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：传感器详细信息 -->
      <div class="right-panel">
        <div v-if="!selectedDevice" class="no-selection">
          <div class="no-selection-icon">
            <i class="fas fa-hand-pointer"></i>
          </div>
          <h3>请选择设备</h3>
          <p>点击左侧设备列表中的设备，查看详细信息和传感器数据</p>
        </div>

        <div v-else class="sensor-detail-panel">
          <!-- 设备信息头部 -->
          <div class="device-header">
            <div class="device-title">
              <h3>
                <i class="fas fa-microchip"></i>
                {{ selectedDevice.device_name || selectedDevice.name || '未命名设备' }}
              </h3>
              <div class="device-status-badge" :class="getDeviceStatusClass(selectedDevice.status)">
                <i :class="getDeviceStatusIcon(selectedDevice.status)"></i>
                {{ getDeviceStatusText(selectedDevice.status) }}
              </div>
            </div>
            <div class="device-actions">
              <button class="action-btn" @click="refreshSensorData" :disabled="loadingSensors">
                <i class="fas fa-sync-alt" :class="{ 'fa-spin': loadingSensors }"></i>
                刷新数据
              </button>
            </div>
          </div>

          <!-- 传感器概览 -->
          <div class="sensor-overview">
            <div class="overview-stats">
              <div class="overview-item">
                <div class="overview-number">{{ sensorList.length }}</div>
                <div class="overview-label">传感器总数</div>
              </div>
              <div class="overview-item online">
                <div class="overview-number">{{ onlineSensorCount }}</div>
                <div class="overview-label">在线传感器</div>
              </div>
              <div class="overview-item offline">
                <div class="overview-number">{{ offlineSensorCount }}</div>
                <div class="overview-label">离线传感器</div>
              </div>
              <div class="overview-item gateways">
                <div class="overview-number">{{ sensorsByGateway.length }}</div>
                <div class="overview-label">网关数量</div>
              </div>
            </div>
          </div>

          <!-- 传感器数据列表 -->
          <div class="sensor-data-section">
            <div class="section-header">
              <h4><i class="fas fa-thermometer-half"></i> 传感器数据</h4>
              <div class="sensor-controls">
                <div class="view-toggle">
                  <button
                    class="toggle-btn"
                    :class="{ 'active': sensorViewMode === 'list' }"
                    @click="sensorViewMode = 'list'"
                    title="列表视图"
                  >
                    <i class="fas fa-list"></i>
                  </button>
                  <button
                    class="toggle-btn"
                    :class="{ 'active': sensorViewMode === 'chart' }"
                    @click="sensorViewMode = 'chart'"
                    title="图表视图"
                  >
                    <i class="fas fa-chart-line"></i>
                  </button>
                </div>
                <div class="sensor-filter">
                  <select v-model="sensorFilter" class="filter-select">
                    <option value="all">全部传感器</option>
                    <option value="online">在线传感器</option>
                    <option value="offline">离线传感器</option>
                    <option value="temperature">温度传感器</option>
                    <option value="humidity">湿度传感器</option>
                    <option value="pressure">压力传感器</option>
                  </select>
                </div>
              </div>
            </div>

            <div v-if="loadingSensors" class="loading-container">
              <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
              </div>
              <span>加载传感器数据中...</span>
            </div>

            <div v-else-if="filteredSensorList.length === 0" class="no-data-container">
              <i class="fas fa-thermometer-half"></i>
              <p>{{ sensorList.length === 0 ? '该设备暂无传感器数据' : '没有符合条件的传感器' }}</p>
            </div>

            <!-- 列表视图 -->
            <div v-else-if="sensorViewMode === 'list'" class="sensor-list">
              <div
                v-for="sensor in filteredSensorList"
                :key="sensor.id"
                class="sensor-item"
                @click="selectSensor(sensor)"
                @mouseenter="hoverSensor(sensor, $event)"
                @mouseleave="unhoverSensor"
                :class="{ 'selected': selectedSensor && selectedSensor.id === sensor.id }"
              >
                <div class="sensor-item-header">
                  <div class="sensor-icon">
                    <i :class="getSensorIcon(sensor.sensor_type)"></i>
                  </div>
                  <div class="sensor-info">
                    <div class="sensor-name">{{ sensor.sensor_name || sensor.name || '未命名传感器' }}</div>
                    <div class="sensor-gateway">{{ sensor.gateway ? sensor.gateway.name : '未知网关' }}</div>
                  </div>
                  <div class="sensor-status" :class="getSensorStatusClass(sensor.status)">
                    <i :class="getSensorStatusIcon(sensor.status)"></i>
                  </div>
                </div>
                <div class="sensor-item-body">
                  <div class="sensor-value-display">
                    <div class="value-main">
                      <span class="value">{{ formatSensorValue(sensor.sensor_value || sensor.current_value) }}</span>
                      <span class="unit">{{ sensor.unit || '' }}</span>
                    </div>
                    <div class="value-type">{{ getSensorTypeText(sensor.sensor_type) }}</div>
                  </div>
                  <div class="sensor-meta">
                    <div class="meta-item">
                      <i class="fas fa-clock"></i>
                      <span>{{ formatTime(sensor.data_updated_at || sensor.updated_at) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 图表视图 -->
            <div v-else-if="sensorViewMode === 'chart'" class="sensor-charts">
              <div class="chart-container" v-for="sensor in filteredSensorList.slice(0, 4)" :key="sensor.id">
                <div class="chart-header">
                  <div class="chart-title">
                    <i :class="getSensorIcon(sensor.sensor_type)"></i>
                    <span>{{ sensor.sensor_name || '未命名传感器' }}</span>
                  </div>
                  <div class="chart-value">
                    <span class="value">{{ formatSensorValue(sensor.sensor_value || sensor.current_value) }}</span>
                    <span class="unit">{{ sensor.unit || '' }}</span>
                  </div>
                </div>
                <div class="mini-chart" :ref="`chart-${sensor.id}`">
                  <!-- 简单的SVG图表 -->
                  <svg width="100%" height="60" class="sensor-chart">
                    <defs>
                      <linearGradient :id="`gradient-${sensor.id}`" x1="0%" y1="0%" x2="0%" y2="100%">
                        <stop offset="0%" :style="`stop-color:${getSensorColor(sensor.sensor_type)};stop-opacity:0.8`" />
                        <stop offset="100%" :style="`stop-color:${getSensorColor(sensor.sensor_type)};stop-opacity:0.1`" />
                      </linearGradient>
                    </defs>
                    <polyline
                      :points="generateChartPoints(sensor)"
                      fill="none"
                      :stroke="getSensorColor(sensor.sensor_type)"
                      stroke-width="2"
                    />
                    <polygon
                      :points="generateChartArea(sensor)"
                      :fill="`url(#gradient-${sensor.id})`"
                    />
                  </svg>
                </div>
                <div class="chart-info">
                  <span class="chart-status" :class="getSensorStatusClass(sensor.status)">
                    {{ sensor.status === 1 ? '在线' : '离线' }}
                  </span>
                  <span class="chart-time">{{ formatTime(sensor.data_updated_at || sensor.updated_at) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getDeviceList, getProjectDevices, getDeviceGateways, getDeviceCameras } from '@/api/device'
import { getSensorListByGatewayCode } from '@/api/sensor'
import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader'
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'

export default {
  name: 'DeviceManagement',
  props: {
    projectId: {
      type: [String, Number],
      required: true
    },
    selectedProject: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      loading: false,
      loadingSensors: false,
      deviceList: [],
      selectedDevice: null,
      selectedSensor: null,
      sensorList: [],
      sensorFilter: 'all',
      sensorViewMode: 'list', // 'list' 或 'chart'
      deviceSearchText: '',
      isFullscreen: false,
      deviceStats: {
        total: 0,
        online: 0,
        offline: 0,
        sensors: 0
      },
      // 3D模型相关
      modelLoading: true,
      loadingText: '初始化3D模型...',
      loadingProgress: 0,
      scene: null,
      camera: null,
      renderer: null,
      controls: null,
      model: null,
      autoRotate: true,
      showSensorMarkers: true,
      sensorMarkers: [],
      // 传感器悬浮提示
      hoveredSensor: null,
      tooltipStyle: {
        left: '0px',
        top: '0px',
        display: 'none'
      },
      // 数据更新定时器
      dataUpdateTimer: null
    }
  },
  computed: {
    projectName() {
      return this.selectedProject?.name || '未知项目'
    },
    // 按网关分组的传感器数据
    sensorsByGateway() {
      const grouped = {}
      this.sensorList.forEach(sensor => {
        if (sensor.gateway) {
          const gatewayId = sensor.gateway.id
          if (!grouped[gatewayId]) {
            grouped[gatewayId] = {
              gateway: sensor.gateway,
              sensors: []
            }
          }
          grouped[gatewayId].sensors.push(sensor)
        }
      })
      return Object.values(grouped)
    },
    // 在线设备百分比
    onlinePercentage() {
      return this.deviceStats.total > 0 ? Math.round((this.deviceStats.online / this.deviceStats.total) * 100) : 0
    },
    // 活跃传感器数量
    activeSensors() {
      return this.sensorList.filter(sensor => sensor.status === 1).length
    },
    // 在线传感器数量
    onlineSensorCount() {
      return this.sensorList.filter(sensor => sensor.status === 1).length
    },
    // 离线传感器数量
    offlineSensorCount() {
      return this.sensorList.filter(sensor => sensor.status !== 1).length
    },
    // 过滤后的传感器列表
    filteredSensorList() {
      if (this.sensorFilter === 'all') {
        return this.sensorList
      } else if (this.sensorFilter === 'online') {
        return this.sensorList.filter(sensor => sensor.status === 1)
      } else if (this.sensorFilter === 'offline') {
        return this.sensorList.filter(sensor => sensor.status !== 1)
      } else {
        // 按传感器类型过滤
        const typeMap = {
          'temperature': [1, 'temperature'],
          'humidity': [2, 'humidity'],
          'pressure': [3, 'pressure']
        }
        const types = typeMap[this.sensorFilter] || []
        return this.sensorList.filter(sensor => types.includes(sensor.sensor_type))
      }
    },
    // 过滤后的设备列表
    filteredDeviceList() {
      if (!this.deviceSearchText) {
        return this.deviceList
      }
      const searchText = this.deviceSearchText.toLowerCase()
      return this.deviceList.filter(device => {
        const deviceName = (device.device_name || device.name || '').toLowerCase()
        const deviceCode = (device.device_code || device.code || '').toLowerCase()
        const deviceType = (device.device_type || device.type || '').toLowerCase()
        return deviceName.includes(searchText) ||
               deviceCode.includes(searchText) ||
               deviceType.includes(searchText)
      })
    }
  },
  mounted() {
    this.fetchDeviceData()
    this.init3DModel()
    this.startDataUpdate()
  },
  beforeDestroy() {
    this.cleanup()
  },
  methods: {
    // 初始化3D模型
    async init3DModel() {
      try {
        await this.$nextTick()
        const container = this.$refs.modelContainer
        if (!container) {
          console.error('模型容器未找到')
          return
        }

        // 创建场景
        this.scene = new THREE.Scene()
        this.scene.background = null // 透明背景

        // 创建相机
        const width = container.clientWidth
        const height = container.clientHeight
        this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000)
        this.camera.position.set(0, 50, 100)

        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({
          antialias: true,
          alpha: true,
          preserveDrawingBuffer: true
        })
        this.renderer.setSize(width, height)
        this.renderer.setClearColor(0x000000, 0) // 透明背景
        this.renderer.shadowMap.enabled = true
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
        container.appendChild(this.renderer.domElement)

        // 创建控制器
        this.controls = new OrbitControls(this.camera, this.renderer.domElement)
        this.controls.enableDamping = true
        this.controls.dampingFactor = 0.05
        this.controls.autoRotate = this.autoRotate
        this.controls.autoRotateSpeed = 2.0

        // 添加光源
        this.addLights()

        // 初始化时不加载模型，等待用户选择设备
        this.modelLoading = false
        this.loadingText = '请选择设备以查看3D模型'

        // 开始渲染循环
        this.animate()

        // 监听窗口大小变化
        window.addEventListener('resize', this.onWindowResize)

      } catch (error) {
        console.error('3D模型初始化失败:', error)
        this.modelLoading = false
        this.loadingText = '3D模型加载失败'
      }
    },

    // 添加光源
    addLights() {
      // 环境光
      const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
      this.scene.add(ambientLight)

      // 方向光
      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
      directionalLight.position.set(50, 50, 50)
      directionalLight.castShadow = true
      directionalLight.shadow.mapSize.width = 2048
      directionalLight.shadow.mapSize.height = 2048
      this.scene.add(directionalLight)

      // 点光源
      const pointLight = new THREE.PointLight(0x7fdbff, 0.5, 200)
      pointLight.position.set(0, 30, 0)
      this.scene.add(pointLight)
    },

    // 加载3D模型
    async load3DModel() {
      this.loadingText = '加载3D模型...'
      this.loadingProgress = 0

      const loader = new GLTFLoader()

      // 设置DRACO解码器
      const dracoLoader = new DRACOLoader()
      dracoLoader.setDecoderPath('https://www.gstatic.com/draco/versioned/decoders/1.5.6/')
      dracoLoader.setDecoderConfig({ type: 'js' })
      loader.setDRACOLoader(dracoLoader)

      try {
        // 优先尝试本地模型
        const gltf = await this.loadModelWithProgress(loader, '/models/xsqt.glb')
        this.processLoadedModel(gltf.scene)
      } catch (localError) {
        console.warn('本地模型加载失败，尝试远程模型:', localError)
        try {
          // 尝试远程模型
          const gltf = await this.loadModelWithProgress(loader, 'https://brtt-bucket.oss-cn-beijing.aliyuncs.com/xsqt.glb')
          this.processLoadedModel(gltf.scene)
        } catch (remoteError) {
          console.error('远程模型加载失败:', remoteError)
          this.createFallbackModel()
        }
      }
    },

    // 带进度的模型加载
    loadModelWithProgress(loader, url) {
      return new Promise((resolve, reject) => {
        loader.load(
          url,
          (gltf) => resolve(gltf),
          (progress) => {
            if (progress.total > 0) {
              this.loadingProgress = Math.round((progress.loaded / progress.total) * 100)
              this.loadingText = `加载3D模型... ${this.loadingProgress}%`
            }
          },
          (error) => reject(error)
        )
      })
    },

    // 处理加载的模型
    processLoadedModel(modelScene) {
      this.model = modelScene

      // 计算模型包围盒
      const box = new THREE.Box3().setFromObject(this.model)
      const size = box.getSize(new THREE.Vector3())
      const maxDim = Math.max(size.x, size.y, size.z)

      // 缩放模型
      const scaleFactor = 80 / maxDim
      this.model.scale.set(scaleFactor, scaleFactor, scaleFactor)

      // 居中模型
      const center = box.getCenter(new THREE.Vector3())
      this.model.position.set(-center.x * scaleFactor, -center.y * scaleFactor, -center.z * scaleFactor)

      // 添加到场景
      this.scene.add(this.model)

      // 模型加载完成
      this.modelLoading = false
      this.loadingText = '模型加载完成'

      console.log('3D模型加载成功')
    },

    // 创建备用模型
    createFallbackModel() {
      const geometry = new THREE.BoxGeometry(50, 30, 80)
      const material = new THREE.MeshPhongMaterial({
        color: 0x7fdbff,
        transparent: true,
        opacity: 0.8
      })
      this.model = new THREE.Mesh(geometry, material)
      this.scene.add(this.model)

      this.modelLoading = false
      this.loadingText = '使用备用模型'
      console.log('使用备用3D模型')
    },

    // 根据设备加载对应的3D模型
    async loadDeviceModel(device) {
      if (!this.scene) {
        console.warn('3D场景未初始化，无法加载设备模型')
        return
      }

      // 清除当前模型
      if (this.model) {
        this.scene.remove(this.model)
        this.model = null
      }

      // 清除传感器标记
      this.clearSensorMarkers()

      this.modelLoading = true
      this.loadingText = `正在加载设备模型: ${device.device_name || device.name || '未命名设备'}`
      this.loadingProgress = 0

      try {
        // 根据设备类型或名称选择不同的模型
        let modelUrl = this.getDeviceModelUrl(device)

        console.log(`为设备 ${device.device_name || device.name} 加载模型: ${modelUrl}`)

        const loader = new GLTFLoader()
        const dracoLoader = new DRACOLoader()
        dracoLoader.setDecoderPath('/draco/')
        loader.setDRACOLoader(dracoLoader)

        // 加载模型
        const gltf = await new Promise((resolve, reject) => {
          loader.load(
            modelUrl,
            (gltf) => resolve(gltf),
            (progress) => {
              if (progress.lengthComputable) {
                this.loadingProgress = Math.round((progress.loaded / progress.total) * 100)
              }
            },
            (error) => reject(error)
          )
        })

        this.processLoadedModel(gltf.scene)
        console.log(`设备 ${device.device_name || device.name} 的3D模型加载成功`)

      } catch (error) {
        console.error(`设备模型加载失败:`, error)
        this.loadingText = '模型加载失败，使用默认模型'
        this.createFallbackModel()
      }
    },

    // 获取设备对应的模型URL
    getDeviceModelUrl(device) {
      // 根据设备类型、名称或其他属性返回对应的模型URL
      const deviceType = device.device_type || device.type || ''
      const deviceName = device.device_name || device.name || ''

      // 定义设备类型与模型的映射关系
      const modelMapping = {
        // 桥梁监测设备
        'bridge': 'http://glb.bjbrtt.com/xsqt.glb',
        'bridge_monitor': 'http://glb.bjbrtt.com/xsqt.glb',
        '桥梁': 'http://glb.bjbrtt.com/xsqt.glb',
        '桥梁监测': 'http://glb.bjbrtt.com/xsqt.glb',

        // 建筑监测设备
        'building': 'http://glb.bjbrtt.com/building.glb',
        'building_monitor': 'http://glb.bjbrtt.com/building.glb',
        '建筑': 'http://glb.bjbrtt.com/building.glb',
        '建筑监测': 'http://glb.bjbrtt.com/building.glb',

        // 隧道监测设备
        'tunnel': 'http://glb.bjbrtt.com/tunnel.glb',
        'tunnel_monitor': 'http://glb.bjbrtt.com/tunnel.glb',
        '隧道': 'http://glb.bjbrtt.com/tunnel.glb',
        '隧道监测': 'http://glb.bjbrtt.com/tunnel.glb',

        // 默认模型
        'default': 'http://glb.bjbrtt.com/xsqt.glb'
      }

      // 首先尝试根据设备类型匹配
      if (deviceType && modelMapping[deviceType.toLowerCase()]) {
        return modelMapping[deviceType.toLowerCase()]
      }

      // 然后尝试根据设备名称匹配
      for (const [key, url] of Object.entries(modelMapping)) {
        if (deviceName.toLowerCase().includes(key)) {
          return url
        }
      }

      // 如果都没有匹配，返回默认模型
      return modelMapping.default
    },

    // 获取设备数据
    async fetchDeviceData() {
      this.loading = true
      try {
        console.log('获取项目设备数据，项目ID:', this.projectId)

        // 优先使用项目专用API获取设备列表
        let response
        try {
          response = await getProjectDevices(this.projectId)
        } catch (error) {
          console.warn('项目专用设备API失败，使用通用API:', error)
          // 如果项目专用API失败，使用通用API
          response = await getDeviceList({ project_id: this.projectId })
        }

        if (response && response.code === 0 && response.data) {
          this.deviceList = response.data.list || response.data || []

          // 为每个设备获取网关和摄像头信息
          for (let device of this.deviceList) {
            try {
              // 获取设备的网关信息
              const gatewayResponse = await getDeviceGateways(device.id)
              if (gatewayResponse && gatewayResponse.code === 0) {
                device.gateways = gatewayResponse.data || []
              }

              // 获取设备的摄像头信息
              const cameraResponse = await getDeviceCameras(device.id)
              if (cameraResponse && cameraResponse.code === 0) {
                device.cameras = cameraResponse.data || []
              }
            } catch (error) {
              console.error(`获取设备 ${device.id} 的详细信息失败:`, error)
            }
          }

          this.calculateDeviceStats()
        } else {
          console.warn('获取设备列表失败:', response)
          this.deviceList = []
        }
      } catch (error) {
        console.error('获取设备数据失败:', error)
        this.deviceList = []
      } finally {
        this.loading = false
      }
    },

    // 计算设备统计信息
    calculateDeviceStats() {
      this.deviceStats.total = this.deviceList.length
      this.deviceStats.online = this.deviceList.filter(device => device.status === 1).length
      this.deviceStats.offline = this.deviceStats.total - this.deviceStats.online

      // 计算传感器总数
      let totalSensors = 0
      this.deviceList.forEach(device => {
        if (device.gateways) {
          device.gateways.forEach(gateway => {
            totalSensors += gateway.sensor_count || 0
          })
        }
      })
      this.deviceStats.sensors = totalSensors
    },

    // 获取设备传感器数量
    getDeviceSensorCount(device) {
      if (!device.gateways) return 0
      let count = 0
      device.gateways.forEach(gateway => {
        count += gateway.sensor_count || 0
      })
      return count
    },

    // 选择设备
    async selectDevice(device) {
      console.log('选择设备:', device)
      console.log('设备网关信息:', device.gateways)
      this.selectedDevice = device
      this.selectedSensor = null

      // 根据选择的设备加载对应的3D模型
      await this.loadDeviceModel(device)

      await this.fetchSensorData(device)
      this.updateSensorMarkers()
    },

    // 选择传感器
    selectSensor(sensor) {
      this.selectedSensor = sensor
      this.highlightSensorInModel(sensor)
    },

    // 悬停传感器
    hoverSensor(sensor, event) {
      this.hoveredSensor = sensor
      this.updateTooltipPosition(event)
    },

    // 取消悬停传感器
    unhoverSensor() {
      this.hoveredSensor = null
      this.tooltipStyle.display = 'none'
    },

    // 更新提示框位置
    updateTooltipPosition(event) {
      if (this.hoveredSensor) {
        this.tooltipStyle = {
          left: (event.clientX + 10) + 'px',
          top: (event.clientY - 10) + 'px',
          display: 'block'
        }
      }
    },

    // 在3D模型中高亮传感器
    highlightSensorInModel(sensor) {
      // 清除之前的高亮
      this.clearSensorHighlight()

      // 在模型上添加高亮标记
      if (this.model && sensor) {
        const position = this.getSensorPosition(sensor)
        if (position) {
          this.addSensorHighlight(position, sensor)
        }
      }
    },

    // 获取传感器在3D模型中的位置
    getSensorPosition(sensor) {
      // 根据传感器类型和ID计算在模型中的位置
      // 这里使用模拟位置，实际应用中应该从传感器数据中获取
      const positions = {
        1: { x: 20, y: 10, z: 30 },   // 温度传感器
        2: { x: -20, y: 15, z: 25 },  // 湿度传感器
        3: { x: 0, y: 20, z: -30 },   // 压力传感器
        4: { x: 30, y: 5, z: 0 },     // 角度传感器
        5: { x: -30, y: 8, z: 10 },   // 力传感器
        6: { x: 15, y: 25, z: -15 },  // 振动传感器
        7: { x: -15, y: 12, z: 35 }   // 位移传感器
      }

      const basePosition = positions[sensor.sensor_type] || { x: 0, y: 0, z: 0 }

      // 根据传感器ID添加一些随机偏移
      const offset = (sensor.id % 10) * 2
      return {
        x: basePosition.x + offset,
        y: basePosition.y + offset * 0.5,
        z: basePosition.z + offset * 0.3
      }
    },

    // 添加传感器高亮标记
    addSensorHighlight(position, sensor) {
      const geometry = new THREE.SphereGeometry(2, 16, 16)
      const material = new THREE.MeshBasicMaterial({
        color: sensor.status === 1 ? 0x00ff00 : 0xff0000,
        transparent: true,
        opacity: 0.8
      })

      const highlight = new THREE.Mesh(geometry, material)
      highlight.position.set(position.x, position.y, position.z)
      highlight.userData = { type: 'sensorHighlight', sensor: sensor }

      this.scene.add(highlight)
    },

    // 清除传感器高亮
    clearSensorHighlight() {
      const highlights = this.scene.children.filter(child =>
        child.userData && child.userData.type === 'sensorHighlight'
      )
      highlights.forEach(highlight => {
        this.scene.remove(highlight)
      })
    },

    // 更新传感器标记
    updateSensorMarkers() {
      // 清除现有标记
      this.clearSensorMarkers()

      if (!this.showSensorMarkers || !this.model || this.sensorList.length === 0) {
        return
      }

      // 为每个传感器添加标记
      this.sensorList.forEach(sensor => {
        const position = this.getSensorPosition(sensor)
        if (position) {
          this.addSensorMarker(position, sensor)
        }
      })
    },

    // 添加传感器标记
    addSensorMarker(position, sensor) {
      const geometry = new THREE.SphereGeometry(1.5, 12, 12)
      const material = new THREE.MeshBasicMaterial({
        color: sensor.status === 1 ? 0x7fdbff : 0xff4136,
        transparent: true,
        opacity: 0.7
      })

      const marker = new THREE.Mesh(geometry, material)
      marker.position.set(position.x, position.y, position.z)
      marker.userData = {
        type: 'sensorMarker',
        sensor: sensor,
        clickable: true
      }

      this.scene.add(marker)
      this.sensorMarkers.push(marker)

      // 添加传感器标签
      this.addSensorLabel(position, sensor)
    },

    // 添加传感器标签
    addSensorLabel(position, sensor) {
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      canvas.width = 128
      canvas.height = 64

      context.fillStyle = 'rgba(0, 0, 0, 0.8)'
      context.fillRect(0, 0, canvas.width, canvas.height)

      context.fillStyle = '#ffffff'
      context.font = '12px Arial'
      context.textAlign = 'center'
      context.fillText(sensor.sensor_name || '传感器', canvas.width / 2, 20)
      context.fillText(`${this.formatSensorValue(sensor.sensor_value)} ${sensor.unit || ''}`, canvas.width / 2, 40)

      const texture = new THREE.CanvasTexture(canvas)
      const material = new THREE.SpriteMaterial({ map: texture, transparent: true })
      const sprite = new THREE.Sprite(material)

      sprite.position.set(position.x, position.y + 5, position.z)
      sprite.scale.set(8, 4, 1)
      sprite.userData = { type: 'sensorLabel', sensor: sensor }

      this.scene.add(sprite)
      this.sensorMarkers.push(sprite)
    },

    // 清除传感器标记
    clearSensorMarkers() {
      this.sensorMarkers.forEach(marker => {
        this.scene.remove(marker)
      })
      this.sensorMarkers = []
    },

    // 获取传感器数据
    async fetchSensorData(device) {
      console.log('开始获取传感器数据，设备:', device.device_name || device.name)

      if (!device.gateways || device.gateways.length === 0) {
        console.log('设备没有网关信息，清空传感器列表')
        this.sensorList = []
        return
      }

      console.log(`设备有 ${device.gateways.length} 个网关`)
      this.loadingSensors = true
      this.sensorList = []

      try {
        // 为每个网关获取传感器数据，优先使用网关编码
        for (let gateway of device.gateways) {
          try {
            const gatewayCode = gateway.gateway_code || gateway.code
            const gatewayName = gateway.gateway_name || gateway.name || '未命名网关'

            console.log(`正在获取网关 ${gateway.id} (${gatewayName}) 的传感器数据`)
            console.log(`网关编码: ${gatewayCode}`)

            let sensors = []

            // 只使用网关编码获取传感器数据
            if (gatewayCode) {
              try {
                console.log(`使用网关编码获取传感器数据: ${gatewayCode}`)
                const response = await getSensorListByGatewayCode(gatewayCode)
                console.log(`网关编码 ${gatewayCode} 传感器API响应:`, response)

                if (response && response.code === 0 && response.data) {
                  // 处理API返回的数据结构：{ gateway: {...}, list: [...] }
                  if (response.data.list && Array.isArray(response.data.list)) {
                    sensors = response.data.list
                    // 如果有网关信息，更新网关名称
                    if (response.data.gateway) {
                      gatewayName = response.data.gateway.gateway_name || gatewayName
                    }
                  } else if (Array.isArray(response.data)) {
                    sensors = response.data
                  } else if (response.data.sensors && Array.isArray(response.data.sensors)) {
                    sensors = response.data.sensors
                  } else {
                    console.warn(`网关编码 ${gatewayCode} 返回的数据结构不符合预期:`, response.data)
                  }

                  console.log(`通过网关编码获取到 ${sensors.length} 个传感器`)
                }
              } catch (codeError) {
                console.error(`使用网关编码 ${gatewayCode} 获取传感器失败:`, codeError)
              }
            } else {
              console.warn(`网关 ${gatewayName} 没有网关编码，跳过传感器数据获取`)
            }

            // 为每个传感器添加网关信息
            if (sensors.length > 0) {
              sensors.forEach(sensor => {
                sensor.gateway = {
                  id: gateway.id,
                  name: gatewayName,
                  code: gatewayCode || gateway.id
                }
              })
              this.sensorList.push(...sensors)
              console.log(`网关 ${gatewayName} 成功添加 ${sensors.length} 个传感器`)
            } else {
              console.warn(`网关 ${gatewayName} 没有获取到传感器数据`)
            }

          } catch (error) {
            console.error(`获取网关 ${gateway.id} 的传感器数据失败:`, error)
          }
        }

        console.log(`总共获取到 ${this.sensorList.length} 个传感器`)
        console.log('传感器列表:', this.sensorList)
        console.log('按网关分组的传感器:', this.sensorsByGateway)

        // 强制更新视图
        this.$forceUpdate()
      } catch (error) {
        console.error('获取传感器数据失败:', error)
      } finally {
        this.loadingSensors = false
      }
    },

    // 刷新传感器数据
    async refreshSensorData() {
      if (this.selectedDevice) {
        console.log('手动刷新传感器数据')
        await this.fetchSensorData(this.selectedDevice)
        this.updateSensorMarkers()
      }
    },

    // 3D模型控制方法
    resetModelView() {
      if (this.camera && this.controls) {
        this.camera.position.set(0, 50, 100)
        this.controls.reset()
      }
    },

    toggleAutoRotate() {
      this.autoRotate = !this.autoRotate
      if (this.controls) {
        this.controls.autoRotate = this.autoRotate
      }
    },

    toggleSensorMarkers() {
      this.showSensorMarkers = !this.showSensorMarkers
      this.updateSensorMarkers()
    },

    // 渲染循环
    animate() {
      if (!this.renderer || !this.scene || !this.camera) return

      requestAnimationFrame(this.animate)

      // 更新控制器
      if (this.controls) {
        this.controls.update()
      }

      // 渲染场景
      this.renderer.render(this.scene, this.camera)
    },

    // 窗口大小变化处理
    onWindowResize() {
      if (!this.camera || !this.renderer || !this.$refs.modelContainer) return

      const container = this.$refs.modelContainer
      const width = container.clientWidth
      const height = container.clientHeight

      this.camera.aspect = width / height
      this.camera.updateProjectionMatrix()
      this.renderer.setSize(width, height)
    },

    // 开始数据更新定时器
    startDataUpdate() {
      // 每30秒更新一次传感器数据
      this.dataUpdateTimer = setInterval(() => {
        if (this.selectedDevice && !this.loadingSensors) {
          this.refreshSensorData()
        }
      }, 30000)
    },

    // 清理资源
    cleanup() {
      // 清理定时器
      if (this.dataUpdateTimer) {
        clearInterval(this.dataUpdateTimer)
        this.dataUpdateTimer = null
      }

      // 清理3D资源
      if (this.renderer) {
        this.renderer.dispose()
        if (this.$refs.modelContainer && this.renderer.domElement) {
          this.$refs.modelContainer.removeChild(this.renderer.domElement)
        }
      }

      if (this.scene) {
        this.scene.clear()
      }

      // 移除事件监听
      window.removeEventListener('resize', this.onWindowResize)
    },

    // 获取设备状态样式类
    getDeviceStatusClass(status) {
      return status === 1 ? 'online' : 'offline'
    },

    // 获取设备状态图标
    getDeviceStatusIcon(status) {
      return status === 1 ? 'fas fa-check-circle' : 'fas fa-times-circle'
    },

    // 获取设备状态文本
    getDeviceStatusText(status) {
      return status === 1 ? '在线' : '离线'
    },

    // 获取传感器图标
    getSensorIcon(sensorType) {
      const iconMap = {
        1: 'fas fa-thermometer-half', // 温度
        2: 'fas fa-tint', // 湿度
        3: 'fas fa-gauge-high', // 压力
        4: 'fas fa-compass', // 角度/风速
        5: 'fas fa-weight-hanging', // 力
        6: 'fas fa-wave-square', // 振动
        7: 'fas fa-arrows-alt-h', // 位移
        'temperature': 'fas fa-thermometer-half',
        'humidity': 'fas fa-tint',
        'pressure': 'fas fa-gauge-high',
        'force': 'fas fa-weight-hanging',
        'angle': 'fas fa-compass',
        'vibration': 'fas fa-wave-square',
        'displacement': 'fas fa-arrows-alt-h'
      }
      return iconMap[sensorType] || 'fas fa-microchip'
    },

    // 获取传感器状态样式类
    getSensorStatusClass(status) {
      return status === 1 ? 'sensor-online' : 'sensor-offline'
    },

    // 获取传感器状态图标
    getSensorStatusIcon(status) {
      return status === 1 ? 'fas fa-circle' : 'fas fa-circle'
    },

    // 获取传感器类型文本
    getSensorTypeText(sensorType) {
      const typeMap = {
        1: '温度传感器',
        2: '湿度传感器',
        3: '压力传感器',
        4: '角度传感器',
        5: '力传感器',
        6: '振动传感器',
        7: '位移传感器',
        'temperature': '温度传感器',
        'humidity': '湿度传感器',
        'pressure': '压力传感器',
        'force': '力传感器',
        'angle': '角度传感器',
        'vibration': '振动传感器',
        'displacement': '位移传感器'
      }
      return typeMap[sensorType] || '未知类型'
    },

    // 格式化传感器值
    formatSensorValue(value) {
      if (value === null || value === undefined) {
        return '--'
      }
      return typeof value === 'number' ? value.toFixed(2) : value
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return '--'
      const date = new Date(time)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    // 设备健康度评估
    getDeviceHealthScore(device) {
      if (!device.gateways || device.gateways.length === 0) {
        return 0
      }

      let totalSensors = 0
      let onlineSensors = 0

      // 计算该设备的传感器在线率
      if (this.selectedDevice && this.selectedDevice.id === device.id) {
        totalSensors = this.sensorList.length
        onlineSensors = this.sensorList.filter(sensor => sensor.status === 1).length
      } else {
        // 估算传感器数量
        device.gateways.forEach(gateway => {
          totalSensors += gateway.sensor_count || 0
        })
        // 假设在线率基于设备状态
        onlineSensors = device.status === 1 ? totalSensors : 0
      }

      if (totalSensors === 0) return device.status === 1 ? 85 : 0

      const sensorHealthScore = (onlineSensors / totalSensors) * 100
      const deviceStatusScore = device.status === 1 ? 100 : 0

      // 综合评分：传感器在线率70% + 设备状态30%
      return Math.round(sensorHealthScore * 0.7 + deviceStatusScore * 0.3)
    },

    getDeviceHealthClass(device) {
      const score = this.getDeviceHealthScore(device)
      if (score >= 80) return 'health-excellent'
      if (score >= 60) return 'health-good'
      if (score >= 40) return 'health-warning'
      return 'health-critical'
    },

    getDeviceHealthText(device) {
      const score = this.getDeviceHealthScore(device)
      if (score >= 80) return '设备运行良好'
      if (score >= 60) return '设备运行正常'
      if (score >= 40) return '设备需要关注'
      return '设备存在问题'
    },

    // 获取传感器颜色
    getSensorColor(sensorType) {
      const colorMap = {
        1: '#ff6b6b', // 温度 - 红色
        2: '#4ecdc4', // 湿度 - 青色
        3: '#45b7d1', // 压力 - 蓝色
        4: '#96ceb4', // 角度 - 绿色
        5: '#feca57', // 力 - 黄色
        6: '#ff9ff3', // 振动 - 粉色
        7: '#54a0ff', // 位移 - 浅蓝色
        'temperature': '#ff6b6b',
        'humidity': '#4ecdc4',
        'pressure': '#45b7d1',
        'force': '#feca57',
        'angle': '#96ceb4',
        'vibration': '#ff9ff3',
        'displacement': '#54a0ff'
      }
      return colorMap[sensorType] || '#7fdbff'
    },

    // 生成图表点
    generateChartPoints(sensor) {
      // 模拟历史数据点
      const points = []
      const width = 300
      const height = 60
      const dataPoints = 20

      const currentValue = parseFloat(sensor.sensor_value || sensor.current_value || 0)

      for (let i = 0; i < dataPoints; i++) {
        const x = (i / (dataPoints - 1)) * width
        // 生成围绕当前值的模拟数据
        const variation = (Math.random() - 0.5) * 0.2 * currentValue
        const value = Math.max(0, currentValue + variation)
        const y = height - (value / (currentValue * 1.5)) * height
        points.push(`${x},${Math.max(5, Math.min(height - 5, y))}`)
      }

      return points.join(' ')
    },

    // 生成图表区域
    generateChartArea(sensor) {
      const points = this.generateChartPoints(sensor)
      const width = 300
      const height = 60

      return `0,${height} ${points} ${width},${height}`
    },

    // 全屏切换
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen
      if (this.isFullscreen) {
        if (this.$refs.modelContainer.requestFullscreen) {
          this.$refs.modelContainer.requestFullscreen()
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        }
      }
    },

    // 导出模型数据
    exportModelData() {
      const data = {
        project: this.selectedProject,
        devices: this.deviceList,
        selectedDevice: this.selectedDevice,
        sensors: this.sensorList,
        deviceStats: this.deviceStats,
        exportTime: new Date().toISOString()
      }

      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `device-management-${Date.now()}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      this.$message.success('数据导出成功')
    }
  }
}
</script>

<style scoped>
.device-management-page {
  width: 100%;
  height: 100%;
  background: transparent;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-family: 'Microsoft YaHei', sans-serif;
}

/* 页面主体内容 */
.page-content {
  flex: 1;
  display: grid;
  grid-template-columns: 280px 400px 1fr;
  gap: 6px;
  overflow: hidden;
  padding: 6px;
}

/* 左侧面板 */
.left-panel {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: rgba(0, 20, 40, 0.8);
  border: 1px solid rgba(127, 219, 255, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

/* 设备统计仪表板 */
.device-stats-dashboard {
  padding: 6px;
  border-bottom: 1px solid rgba(127, 219, 255, 0.2);
  flex-shrink: 0;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.stats-header h3 {
  margin: 0;
  font-size: 0.85rem;
  color: #7fdbff;
  display: flex;
  align-items: center;
  gap: 4px;
}

.refresh-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: rgba(127, 219, 255, 0.1);
  border: 1px solid rgba(127, 219, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: rgba(127, 219, 255, 0.2);
  border-color: rgba(127, 219, 255, 0.5);
}

.refresh-btn i {
  color: #7fdbff;
  font-size: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 4px;
}

.stat-card {
  background: rgba(0, 30, 60, 0.6);
  border: 1px solid rgba(127, 219, 255, 0.2);
  border-radius: 4px;
  padding: 4px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-align: center;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #7fdbff, transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-card:hover {
  border-color: rgba(127, 219, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(127, 219, 255, 0.15);
}

.stat-card.total::before {
  background: linear-gradient(90deg, transparent, #7fdbff, transparent);
}

.stat-card.online::before {
  background: linear-gradient(90deg, transparent, #2ecc40, transparent);
}

.stat-card.offline::before {
  background: linear-gradient(90deg, transparent, #ff4136, transparent);
}

.stat-card.sensors::before {
  background: linear-gradient(90deg, transparent, #ff851b, transparent);
}

.stat-icon {
  width: 16px;
  height: 16px;
  border-radius: 3px;
  background: linear-gradient(135deg, #7fdbff, #0074d9);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  color: #ffffff;
  margin-bottom: 2px;
  box-shadow: 0 2px 8px rgba(127, 219, 255, 0.3);
}

.stat-card.online .stat-icon {
  background: linear-gradient(135deg, #2ecc40, #27ae60);
  box-shadow: 0 4px 12px rgba(46, 204, 64, 0.3);
}

.stat-card.offline .stat-icon {
  background: linear-gradient(135deg, #ff4136, #e74c3c);
  box-shadow: 0 4px 12px rgba(255, 65, 54, 0.3);
}

.stat-card.sensors .stat-icon {
  background: linear-gradient(135deg, #ff851b, #f39c12);
  box-shadow: 0 4px 12px rgba(255, 133, 27, 0.3);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 0.9rem;
  font-weight: 700;
  color: #7fdbff;
  text-shadow: 0 0 8px rgba(127, 219, 255, 0.5);
  margin-bottom: 1px;
  font-family: 'Orbitron', monospace;
}

.stat-card.online .stat-number {
  color: #2ecc40;
  text-shadow: 0 0 10px rgba(46, 204, 64, 0.5);
}

.stat-card.offline .stat-number {
  color: #ff4136;
  text-shadow: 0 0 10px rgba(255, 65, 54, 0.5);
}

.stat-card.sensors .stat-number {
  color: #ff851b;
  text-shadow: 0 0 10px rgba(255, 133, 27, 0.5);
}

.stat-label {
  font-size: 0.6rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  margin-bottom: 1px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  color: #2ecc40;
}

.stat-progress {
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.stat-progress .progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #2ecc40, #27ae60);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.stat-alert {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  color: #ff4136;
}

.stat-detail {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
}

/* 设备列表区域 */
.device-list-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.section-header {
  padding: 6px 8px;
  border-bottom: 1px solid rgba(127, 219, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 40, 80, 0.4);
  flex-shrink: 0;
}

.section-header h3 {
  margin: 0;
  font-size: 0.9rem;
  color: #7fdbff;
  display: flex;
  align-items: center;
  gap: 6px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: 8px;
  color: rgba(127, 219, 255, 0.6);
  font-size: 12px;
  z-index: 1;
}

.search-input {
  background: rgba(0, 30, 60, 0.6);
  border: 1px solid rgba(127, 219, 255, 0.3);
  border-radius: 4px;
  color: #ffffff;
  padding: 4px 8px 4px 24px;
  font-size: 0.75rem;
  outline: none;
  width: 120px;
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: rgba(127, 219, 255, 0.5);
  background: rgba(0, 30, 60, 0.8);
  width: 140px;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.device-count {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(127, 219, 255, 0.1);
  padding: 3px 6px;
  border-radius: 8px;
  border: 1px solid rgba(127, 219, 255, 0.2);
}

/* 设备列表 */
.device-list {
  flex: 1;
  overflow-y: auto;
  padding: 4px;
}

.device-item {
  background: rgba(0, 40, 80, 0.5);
  border: 1px solid rgba(127, 219, 255, 0.2);
  border-radius: 4px;
  margin-bottom: 4px;
  padding: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.device-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: transparent;
  transition: background 0.3s ease;
}

.device-item:hover {
  border-color: rgba(127, 219, 255, 0.5);
  background: rgba(0, 40, 80, 0.7);
  transform: translateX(3px);
}

.device-item:hover::before {
  background: #7fdbff;
}

.device-item.selected {
  border-color: #7fdbff;
  background: rgba(127, 219, 255, 0.1);
  box-shadow: 0 4px 15px rgba(127, 219, 255, 0.2);
}

.device-item.selected::before {
  background: #7fdbff;
}

.device-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3px;
}

.device-name {
  font-size: 0.75rem;
  font-weight: 600;
  color: #ffffff;
  display: flex;
  align-items: center;
  gap: 4px;
}

.device-name i {
  color: #7fdbff;
  font-size: 10px;
}

.device-indicators {
  display: flex;
  align-items: center;
  gap: 4px;
}

.health-indicator {
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 2px 4px;
  border-radius: 6px;
  font-size: 0.6rem;
  font-weight: 600;
  border: 1px solid;
  transition: all 0.3s ease;
}

.health-indicator.health-excellent {
  background: rgba(46, 204, 64, 0.2);
  color: #2ecc40;
  border-color: rgba(46, 204, 64, 0.3);
}

.health-indicator.health-good {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border-color: rgba(255, 193, 7, 0.3);
}

.health-indicator.health-warning {
  background: rgba(255, 133, 27, 0.2);
  color: #ff851b;
  border-color: rgba(255, 133, 27, 0.3);
}

.health-indicator.health-critical {
  background: rgba(255, 65, 54, 0.2);
  color: #ff4136;
  border-color: rgba(255, 65, 54, 0.3);
}

.health-indicator i {
  font-size: 10px;
}

.device-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px;
}

.device-health-bar {
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.health-bar-bg {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.health-bar-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.health-bar-fill.health-excellent {
  background: linear-gradient(90deg, #2ecc40, #27ae60);
}

.health-bar-fill.health-good {
  background: linear-gradient(90deg, #ffc107, #f39c12);
}

.health-bar-fill.health-warning {
  background: linear-gradient(90deg, #ff851b, #e67e22);
}

.health-bar-fill.health-critical {
  background: linear-gradient(90deg, #ff4136, #e74c3c);
}

.health-text {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.6);
  white-space: nowrap;
}

.info-row {
  display: flex;
  justify-content: space-between;
  font-size: 0.7rem;
}

.info-row .label {
  color: rgba(255, 255, 255, 0.7);
}

.info-row .value {
  color: #ffffff;
  font-weight: 500;
}

/* 中央3D模型面板 */
.center-panel {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: rgba(0, 20, 40, 0.8);
  border: 1px solid rgba(127, 219, 255, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.model-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.model-header {
  padding: 6px 8px;
  border-bottom: 1px solid rgba(127, 219, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 40, 80, 0.4);
  flex-shrink: 0;
}

.model-header h3 {
  margin: 0;
  font-size: 0.9rem;
  color: #7fdbff;
  display: flex;
  align-items: center;
  gap: 6px;
}

.model-controls {
  display: flex;
  gap: 6px;
}

.control-btn {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  background: rgba(127, 219, 255, 0.1);
  border: 1px solid rgba(127, 219, 255, 0.3);
  color: #7fdbff;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover {
  background: rgba(127, 219, 255, 0.2);
  border-color: rgba(127, 219, 255, 0.5);
  transform: translateY(-1px);
}

.control-btn.active {
  background: rgba(127, 219, 255, 0.3);
  border-color: #7fdbff;
}

.control-btn i {
  font-size: 12px;
}

.model-viewer {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: radial-gradient(circle at center, rgba(0, 30, 60, 0.3), rgba(0, 10, 20, 0.8));
}

.model-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 2px solid rgba(127, 219, 255, 0.2);
  border-top: 2px solid #7fdbff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 12px;
}

.loading-text {
  color: #7fdbff;
  font-size: 0.85rem;
  margin-bottom: 8px;
}

.loading-progress {
  width: 150px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.loading-progress .progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #7fdbff, #0074d9);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: -16px;
  right: 0;
  font-size: 0.7rem;
  color: #7fdbff;
}

/* 无设备选择时的提示界面 */
.no-device-selected {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
  color: rgba(255, 255, 255, 0.6);
  max-width: 300px;
}

.no-device-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(127, 219, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  border: 2px solid rgba(127, 219, 255, 0.2);
}

.no-device-icon i {
  font-size: 2.5rem;
  color: rgba(127, 219, 255, 0.4);
}

.no-device-selected h3 {
  margin: 0 0 12px 0;
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
}

.no-device-selected p {
  margin: 0 0 20px 0;
  font-size: 0.9rem;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.6);
}

.model-features {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(127, 219, 255, 0.05);
  border: 1px solid rgba(127, 219, 255, 0.1);
  border-radius: 6px;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
}

.feature-item i {
  color: rgba(127, 219, 255, 0.6);
  font-size: 0.9rem;
  width: 16px;
  text-align: center;
}

.model-info-panel {
  padding: 8px 12px;
  border-top: 1px solid rgba(127, 219, 255, 0.2);
  background: rgba(0, 40, 80, 0.4);
  display: flex;
  gap: 12px;
}

.info-item {
  display: flex;
  gap: 6px;
  font-size: 0.75rem;
}

.info-item .label {
  color: rgba(255, 255, 255, 0.7);
}

.info-item .value {
  color: #7fdbff;
  font-weight: 500;
}

/* 右侧面板 */
.right-panel {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: rgba(0, 20, 40, 0.8);
  border: 1px solid rgba(127, 219, 255, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.no-selection {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  padding: 40px;
}

.no-selection-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(127, 219, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.no-selection-icon i {
  font-size: 1.8rem;
  color: rgba(127, 219, 255, 0.4);
}

.no-selection h3 {
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
}

.no-selection p {
  margin: 0;
  font-size: 0.85rem;
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.6);
}

/* 传感器详情面板 */
.sensor-detail-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.device-header {
  padding: 6px 8px;
  border-bottom: 1px solid rgba(127, 219, 255, 0.2);
  background: rgba(0, 40, 80, 0.4);
  flex-shrink: 0;
}

.device-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.device-title h3 {
  margin: 0;
  font-size: 0.8rem;
  color: #ffffff;
  display: flex;
  align-items: center;
  gap: 4px;
}

.device-title h3 i {
  color: #7fdbff;
}

.device-status-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
}

.device-status-badge.online {
  background: rgba(46, 204, 64, 0.2);
  color: #2ecc40;
  border: 1px solid rgba(46, 204, 64, 0.3);
}

.device-status-badge.offline {
  background: rgba(255, 65, 54, 0.2);
  color: #ff4136;
  border: 1px solid rgba(255, 65, 54, 0.3);
}

.device-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 4px 8px;
  background: rgba(127, 219, 255, 0.1);
  border: 1px solid rgba(127, 219, 255, 0.3);
  border-radius: 4px;
  color: #7fdbff;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.7rem;
}

.action-btn:hover:not(:disabled) {
  background: rgba(127, 219, 255, 0.2);
  border-color: rgba(127, 219, 255, 0.5);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 传感器概览 */
.sensor-overview {
  padding: 4px 6px;
  border-bottom: 1px solid rgba(127, 219, 255, 0.2);
  flex-shrink: 0;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2px;
}

.overview-item {
  text-align: center;
  padding: 3px;
  background: rgba(0, 30, 60, 0.4);
  border: 1px solid rgba(127, 219, 255, 0.2);
  border-radius: 2px;
  transition: all 0.3s ease;
}

.overview-item:hover {
  border-color: rgba(127, 219, 255, 0.4);
  transform: translateY(-1px);
}

.overview-number {
  font-size: 0.8rem;
  font-weight: 700;
  color: #7fdbff;
  margin-bottom: 1px;
  font-family: 'Orbitron', monospace;
}

.overview-item.online .overview-number {
  color: #2ecc40;
}

.overview-item.offline .overview-number {
  color: #ff4136;
}

.overview-item.gateways .overview-number {
  color: #ff851b;
}

.overview-label {
  font-size: 0.55rem;
  color: rgba(255, 255, 255, 0.7);
}

/* 传感器数据区域 */
.sensor-data-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 4px 6px;
}

.sensor-data-section .section-header {
  padding: 0 0 4px 0;
  margin-bottom: 6px;
  background: transparent;
  flex-shrink: 0;
}

.sensor-data-section .section-header h4 {
  margin: 0;
  font-size: 0.8rem;
  color: #7fdbff;
  display: flex;
  align-items: center;
  gap: 4px;
}

.sensor-controls {
  display: flex;
  align-items: center;
  gap: 6px;
}

.view-toggle {
  display: flex;
  background: rgba(0, 30, 60, 0.6);
  border: 1px solid rgba(127, 219, 255, 0.3);
  border-radius: 4px;
  overflow: hidden;
}

.toggle-btn {
  padding: 4px 8px;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
}

.toggle-btn:hover {
  background: rgba(127, 219, 255, 0.1);
  color: #7fdbff;
}

.toggle-btn.active {
  background: rgba(127, 219, 255, 0.2);
  color: #7fdbff;
}

.toggle-btn i {
  font-size: 10px;
}

.sensor-filter {
  display: flex;
  align-items: center;
}

.filter-select {
  background: rgba(0, 30, 60, 0.6);
  border: 1px solid rgba(127, 219, 255, 0.3);
  border-radius: 4px;
  color: #ffffff;
  padding: 4px 8px;
  font-size: 0.7rem;
  outline: none;
  cursor: pointer;
}

.filter-select:focus {
  border-color: rgba(127, 219, 255, 0.5);
}

.filter-select option {
  background: rgba(0, 30, 60, 0.9);
  color: #ffffff;
}

/* 传感器列表 */
.sensor-list {
  flex: 1;
  overflow-y: auto;
  padding-right: 4px;
}

.sensor-item {
  background: rgba(0, 40, 80, 0.5);
  border: 1px solid rgba(127, 219, 255, 0.2);
  border-radius: 3px;
  margin-bottom: 3px;
  padding: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.sensor-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: transparent;
  transition: background 0.3s ease;
}

.sensor-item:hover {
  border-color: rgba(127, 219, 255, 0.5);
  background: rgba(0, 40, 80, 0.7);
  transform: translateX(3px);
}

.sensor-item:hover::before {
  background: #7fdbff;
}

.sensor-item.selected {
  border-color: #7fdbff;
  background: rgba(127, 219, 255, 0.1);
  box-shadow: 0 4px 15px rgba(127, 219, 255, 0.2);
}

.sensor-item.selected::before {
  background: #7fdbff;
}

.sensor-item-header {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 3px;
}

.sensor-icon {
  width: 14px;
  height: 14px;
  border-radius: 2px;
  background: rgba(127, 219, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #7fdbff;
  font-size: 8px;
}

.sensor-info {
  flex: 1;
}

.sensor-name {
  font-size: 0.7rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 1px;
}

.sensor-gateway {
  font-size: 0.65rem;
  color: rgba(255, 255, 255, 0.6);
}

.sensor-status {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sensor-status.sensor-online i {
  color: #2ecc40;
  font-size: 8px;
}

.sensor-status.sensor-offline i {
  color: #ff4136;
  font-size: 8px;
}

.sensor-item-body {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sensor-value-display {
  flex: 1;
}

.value-main {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 4px;
}

.value-main .value {
  font-size: 1rem;
  font-weight: 700;
  color: #7fdbff;
  font-family: 'Orbitron', monospace;
}

.value-main .unit {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.7);
}

.value-type {
  font-size: 0.65rem;
  color: rgba(255, 255, 255, 0.6);
}

.sensor-meta {
  text-align: right;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.5);
}

.meta-item i {
  font-size: 10px;
}

/* 传感器提示框 */
.sensor-tooltip {
  position: fixed;
  z-index: 1000;
  background: rgba(0, 20, 40, 0.95);
  border: 1px solid rgba(127, 219, 255, 0.5);
  border-radius: 8px;
  padding: 12px;
  min-width: 200px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.tooltip-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(127, 219, 255, 0.2);
}

.tooltip-header i {
  color: #7fdbff;
  font-size: 16px;
}

.tooltip-header span {
  font-weight: 600;
  color: #ffffff;
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tooltip-row {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
}

.tooltip-row .label {
  color: rgba(255, 255, 255, 0.7);
}

.tooltip-row .value {
  color: #ffffff;
  font-weight: 500;
}

.tooltip-row .value.sensor-online {
  color: #2ecc40;
}

.tooltip-row .value.sensor-offline {
  color: #ff4136;
}

/* 传感器图表视图 */
.sensor-charts {
  flex: 1;
  overflow-y: auto;
  padding: 6px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.chart-container {
  background: rgba(0, 40, 80, 0.5);
  border: 1px solid rgba(127, 219, 255, 0.2);
  border-radius: 6px;
  padding: 8px;
  transition: all 0.3s ease;
}

.chart-container:hover {
  border-color: rgba(127, 219, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(127, 219, 255, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.chart-title {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  color: #ffffff;
}

.chart-title i {
  color: #7fdbff;
  font-size: 10px;
}

.chart-value {
  display: flex;
  align-items: baseline;
  gap: 3px;
}

.chart-value .value {
  font-size: 0.9rem;
  font-weight: 700;
  color: #7fdbff;
  font-family: 'Orbitron', monospace;
}

.chart-value .unit {
  font-size: 0.65rem;
  color: rgba(255, 255, 255, 0.7);
}

.mini-chart {
  margin-bottom: 6px;
  height: 40px;
  overflow: hidden;
}

.sensor-chart {
  width: 100%;
  height: 100%;
}

.chart-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.chart-status {
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: 500;
}

.chart-status.sensor-online {
  background: rgba(46, 204, 64, 0.2);
  color: #2ecc40;
}

.chart-status.sensor-offline {
  background: rgba(255, 65, 54, 0.2);
  color: #ff4136;
}

.chart-time {
  color: rgba(255, 255, 255, 0.6);
}

/* 设备状态样式 */
.device-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.device-status.online {
  background: rgba(46, 204, 64, 0.2);
  color: #2ecc40;
  border: 1px solid rgba(46, 204, 64, 0.3);
}

.device-status.offline {
  background: rgba(255, 65, 54, 0.2);
  color: #ff4136;
  border: 1px solid rgba(255, 65, 54, 0.3);
}

/* 加载和无数据状态 */
.loading-container, .no-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: rgba(127, 219, 255, 0.6);
  text-align: center;
  flex: 1;
}

.loading-container .loading-spinner, .no-data-container i {
  margin-bottom: 16px;
}

.no-data-container i {
  font-size: 3rem;
  opacity: 0.7;
}

.loading-container span, .no-data-container p {
  font-size: 1rem;
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
}

/* 动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.fa-spin {
  animation: spin 1s linear infinite;
}

/* 滚动条样式 */
.device-list::-webkit-scrollbar,
.sensor-list::-webkit-scrollbar {
  width: 6px;
}

.device-list::-webkit-scrollbar-track,
.sensor-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.device-list::-webkit-scrollbar-thumb,
.sensor-list::-webkit-scrollbar-thumb {
  background: rgba(127, 219, 255, 0.3);
  border-radius: 3px;
}

.device-list::-webkit-scrollbar-thumb:hover,
.sensor-list::-webkit-scrollbar-thumb:hover {
  background: rgba(127, 219, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .page-content {
    grid-template-columns: 260px 1fr 300px;
    gap: 10px;
    padding: 10px;
  }
}

@media (max-width: 1400px) {
  .page-content {
    grid-template-columns: 240px 1fr 280px;
    gap: 8px;
    padding: 8px;
  }

  .device-stats-dashboard {
    padding: 10px;
  }

  .stat-card {
    padding: 8px;
  }

  .stat-number {
    font-size: 1.2rem;
  }

  .stat-icon {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }
}

@media (max-width: 1200px) {
  .page-content {
    grid-template-columns: 220px 1fr 260px;
    gap: 6px;
    padding: 6px;
  }

  .model-info-panel {
    flex-direction: column;
    gap: 6px;
  }

  .info-item {
    font-size: 0.7rem;
  }

  .search-input {
    width: 100px;
  }

  .search-input:focus {
    width: 120px;
  }
}

@media (max-width: 1024px) {
  .page-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto;
    gap: 12px;
  }

  .left-panel {
    order: 1;
  }

  .center-panel {
    order: 2;
    min-height: 400px;
  }

  .right-panel {
    order: 3;
    max-height: 300px;
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
  }

  .stat-card {
    padding: 8px;
  }

  .stat-number {
    font-size: 1.4rem;
  }

  .stat-label {
    font-size: 0.75rem;
  }

  .overview-stats {
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
  }

  .overview-item {
    padding: 8px;
  }

  .overview-number {
    font-size: 1.2rem;
  }
}

@media (max-width: 768px) {
  .page-content {
    padding: 8px;
    gap: 8px;
  }

  .device-stats-dashboard {
    padding: 12px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }

  .stat-card {
    padding: 6px;
  }

  .stat-icon {
    width: 28px;
    height: 28px;
    font-size: 12px;
    margin-bottom: 8px;
  }

  .stat-number {
    font-size: 1.2rem;
  }

  .stat-label {
    font-size: 0.7rem;
  }

  .center-panel {
    min-height: 300px;
  }

  .right-panel {
    max-height: 250px;
  }

  .overview-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .device-item {
    padding: 8px;
  }

  .device-name {
    font-size: 0.85rem;
  }

  .info-row {
    font-size: 0.75rem;
  }

  .sensor-item {
    padding: 8px;
  }

  .sensor-name {
    font-size: 0.85rem;
  }

  .value-main .value {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .page-content {
    padding: 6px;
    gap: 6px;
  }

  .stats-header h3 {
    font-size: 1rem;
  }

  .model-header h3 {
    font-size: 1rem;
  }

  .device-title h3 {
    font-size: 1rem;
  }

  .control-btn {
    width: 32px;
    height: 32px;
  }

  .control-btn i {
    font-size: 12px;
  }

  .action-btn {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}
</style>
