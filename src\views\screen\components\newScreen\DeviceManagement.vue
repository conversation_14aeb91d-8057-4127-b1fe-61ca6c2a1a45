<template>
  <div class="device-management-page">
    <!-- 页面主体内容 -->
    <div class="page-content">
      <!-- 左侧：设备列表 -->
      <div class="left-panel">
        <!-- 设备统计信息 - 紧凑设计 -->
        <div class="device-stats-compact">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-icon">
                <i class="fas fa-server"></i>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ deviceStats.total }}</div>
                <div class="stat-label">设备总数</div>
              </div>
            </div>
            <div class="stat-item online">
              <div class="stat-icon">
                <i class="fas fa-wifi"></i>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ deviceStats.online }}</div>
                <div class="stat-label">在线</div>
              </div>
            </div>
            <div class="stat-item offline">
              <div class="stat-icon">
                <i class="fas fa-exclamation-triangle"></i>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ deviceStats.offline }}</div>
                <div class="stat-label">离线</div>
              </div>
            </div>
            <div class="stat-item sensors">
              <div class="stat-icon">
                <i class="fas fa-microchip"></i>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ deviceStats.sensors }}</div>
                <div class="stat-label">传感器</div>
              </div>
            </div>
          </div>
        </div>

        <div class="panel-header">
          <h3><i class="fas fa-list"></i> 设备列表</h3>
          <div class="device-count">共 {{ deviceList.length }} 台设备</div>
        </div>

        <div v-if="loading" class="loading-container">
          <i class="fas fa-spinner fa-spin"></i>
          <span>加载设备数据中...</span>
        </div>

        <div v-else-if="deviceList.length === 0" class="no-data-container">
          <i class="fas fa-database"></i>
          <p>暂无设备数据</p>
        </div>

        <div v-else class="device-list">
          <div
            v-for="device in deviceList"
            :key="device.id"
            class="device-item"
            @click="selectDevice(device)"
            :class="{ 'selected': selectedDevice && selectedDevice.id === device.id }"
          >
            <div class="device-item-header">
              <div class="device-name">
                <i class="fas fa-microchip"></i>
                {{ device.device_name || device.name || '未命名设备' }}
              </div>
              <div class="device-status" :class="getDeviceStatusClass(device.status)">
                <i :class="getDeviceStatusIcon(device.status)"></i>
              </div>
            </div>
            <div class="device-item-body">
              <div class="device-info">
                <div class="info-row">
                  <span class="label">编号:</span>
                  <span class="value">{{ device.device_code || device.code || '-' }}</span>
                </div>
                <div class="info-row">
                  <span class="label">类型:</span>
                  <span class="value">{{ device.device_type || device.type || '-' }}</span>
                </div>
                <div class="info-row">
                  <span class="label">网关:</span>
                  <span class="value">{{ device.gateways ? device.gateways.length : 0 }}</span>
                </div>
                <div class="info-row">
                  <span class="label">摄像头:</span>
                  <span class="value">{{ device.cameras ? device.cameras.length : 0 }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：设备详情和传感器信息 -->
      <div class="right-panel">
        <div v-if="!selectedDevice" class="no-selection">
          <i class="fas fa-hand-pointer"></i>
          <h3>请选择设备</h3>
          <p>点击左侧设备列表中的设备，查看详细信息和传感器数据</p>
        </div>

        <div v-else class="device-detail">
          <!-- 设备详情头部 -->
          <div class="detail-header">
            <h3>
              <i class="fas fa-microchip"></i>
              {{ selectedDevice.device_name || selectedDevice.name || '未命名设备' }}
            </h3>
            <div class="device-status-large" :class="getDeviceStatusClass(selectedDevice.status)">
              <i :class="getDeviceStatusIcon(selectedDevice.status)"></i>
              {{ getDeviceStatusText(selectedDevice.status) }}
            </div>
          </div>

          <!-- 设备基本信息 -->
          <div class="device-basic-info">
            <div class="info-grid">
              <div class="info-card">
                <div class="info-label">设备编号</div>
                <div class="info-value">{{ selectedDevice.device_code || selectedDevice.code || '-' }}</div>
              </div>
              <div class="info-card">
                <div class="info-label">设备类型</div>
                <div class="info-value">{{ selectedDevice.device_type || selectedDevice.type || '-' }}</div>
              </div>
              <div class="info-card">
                <div class="info-label">网关数量</div>
                <div class="info-value">{{ selectedDevice.gateways ? selectedDevice.gateways.length : 0 }}</div>
              </div>
              <div class="info-card">
                <div class="info-label">摄像头数量</div>
                <div class="info-value">{{ selectedDevice.cameras ? selectedDevice.cameras.length : 0 }}</div>
              </div>
            </div>
          </div>

          <!-- 传感器信息 -->
          <div class="sensor-section">
            <div class="section-header">
              <h4><i class="fas fa-thermometer-half"></i> 传感器信息</h4>
              <div class="sensor-controls">
                <div class="sensor-count">共 {{ sensorList.length }} 个传感器 ({{ sensorsByGateway.length }} 个网关)</div>
              </div>
            </div>



            <div v-if="loadingSensors" class="loading-container">
              <i class="fas fa-spinner fa-spin"></i>
              <span>加载传感器数据中...</span>
            </div>

            <div v-else-if="sensorList.length === 0" class="no-data-container">
              <i class="fas fa-thermometer-half"></i>
              <p>该设备暂无传感器数据</p>
            </div>

            <div v-else class="sensor-container">
              <div
                v-for="gatewayGroup in sensorsByGateway"
                :key="gatewayGroup.gateway.id"
                class="gateway-group"
              >
                <!-- 网关信息头部 -->
                <div class="gateway-header">
                  <div class="gateway-info">
                    <i class="fas fa-router"></i>
                    <div class="gateway-details">
                      <div class="gateway-name">{{ gatewayGroup.gateway.name }}</div>
                      <div class="gateway-code">网关号: {{ gatewayGroup.gateway.code }}</div>
                    </div>
                  </div>
                  <div class="sensor-count-badge">
                    {{ gatewayGroup.sensors.length }} 个传感器
                  </div>
                </div>

                <!-- 该网关下的传感器列表 -->
                <div class="sensor-grid">
                  <div
                    v-for="sensor in gatewayGroup.sensors"
                    :key="sensor.id"
                    class="sensor-card"
                  >
                    <div class="sensor-header">
                      <div class="sensor-name">
                        <i :class="getSensorIcon(sensor.sensor_type)"></i>
                        {{ sensor.sensor_name || sensor.name || '未命名传感器' }}
                      </div>
                      <div class="sensor-status" :class="getSensorStatusClass(sensor.status)">
                        <i :class="getSensorStatusIcon(sensor.status)"></i>
                      </div>
                    </div>
                    <div class="sensor-body">
                      <div class="sensor-type">{{ getSensorTypeText(sensor.sensor_type) }}</div>
                      <div class="sensor-value">
                        <span class="value">{{ formatSensorValue(sensor.sensor_value || sensor.current_value) }}</span>
                        <span class="unit">{{ sensor.unit || '' }}</span>
                      </div>
                      <div class="sensor-time">
                        更新时间: {{ formatTime(sensor.data_updated_at || sensor.updated_at) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import { getDeviceList, getProjectDevices, getDeviceGateways, getDeviceCameras } from '@/api/device'
import { getSensorListByGatewayCode } from '@/api/sensor'

export default {
  name: 'DeviceManagement',
  props: {
    projectId: {
      type: [String, Number],
      required: true
    },
    selectedProject: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      loading: false,
      loadingSensors: false,
      deviceList: [],
      selectedDevice: null,
      sensorList: [],
      deviceStats: {
        total: 0,
        online: 0,
        offline: 0,
        sensors: 0
      }
    }
  },
  computed: {
    projectName() {
      return this.selectedProject?.name || '未知项目'
    },
    // 按网关分组的传感器数据
    sensorsByGateway() {
      const grouped = {}
      this.sensorList.forEach(sensor => {
        if (sensor.gateway) {
          const gatewayId = sensor.gateway.id
          if (!grouped[gatewayId]) {
            grouped[gatewayId] = {
              gateway: sensor.gateway,
              sensors: []
            }
          }
          grouped[gatewayId].sensors.push(sensor)
        }
      })
      return Object.values(grouped)
    }
  },
  mounted() {
    this.fetchDeviceData()
  },
  methods: {
    // 获取设备数据
    async fetchDeviceData() {
      this.loading = true
      try {
        console.log('获取项目设备数据，项目ID:', this.projectId)

        // 优先使用项目专用API获取设备列表
        let response
        try {
          response = await getProjectDevices(this.projectId)
        } catch (error) {
          console.warn('项目专用设备API失败，使用通用API:', error)
          // 如果项目专用API失败，使用通用API
          response = await getDeviceList({ project_id: this.projectId })
        }

        if (response && response.code === 0 && response.data) {
          this.deviceList = response.data.list || response.data || []

          // 为每个设备获取网关和摄像头信息
          for (let device of this.deviceList) {
            try {
              // 获取设备的网关信息
              const gatewayResponse = await getDeviceGateways(device.id)
              if (gatewayResponse && gatewayResponse.code === 0) {
                device.gateways = gatewayResponse.data || []
              }

              // 获取设备的摄像头信息
              const cameraResponse = await getDeviceCameras(device.id)
              if (cameraResponse && cameraResponse.code === 0) {
                device.cameras = cameraResponse.data || []
              }
            } catch (error) {
              console.error(`获取设备 ${device.id} 的详细信息失败:`, error)
            }
          }

          this.calculateDeviceStats()
        } else {
          console.warn('获取设备列表失败:', response)
          this.deviceList = []
        }
      } catch (error) {
        console.error('获取设备数据失败:', error)
        this.deviceList = []
      } finally {
        this.loading = false
      }
    },

    // 计算设备统计信息
    calculateDeviceStats() {
      this.deviceStats.total = this.deviceList.length
      this.deviceStats.online = this.deviceList.filter(device => device.status === 1).length
      this.deviceStats.offline = this.deviceStats.total - this.deviceStats.online

      // 计算传感器总数
      let totalSensors = 0
      this.deviceList.forEach(device => {
        if (device.gateways) {
          device.gateways.forEach(gateway => {
            totalSensors += gateway.sensor_count || 0
          })
        }
      })
      this.deviceStats.sensors = totalSensors
    },

    // 选择设备
    async selectDevice(device) {
      console.log('选择设备:', device)
      console.log('设备网关信息:', device.gateways)
      this.selectedDevice = device
      await this.fetchSensorData(device)
    },

    // 获取传感器数据
    async fetchSensorData(device) {
      console.log('开始获取传感器数据，设备:', device.device_name || device.name)

      if (!device.gateways || device.gateways.length === 0) {
        console.log('设备没有网关信息，清空传感器列表')
        this.sensorList = []
        return
      }

      console.log(`设备有 ${device.gateways.length} 个网关`)
      this.loadingSensors = true
      this.sensorList = []

      try {
        // 为每个网关获取传感器数据，优先使用网关编码
        for (let gateway of device.gateways) {
          try {
            const gatewayCode = gateway.gateway_code || gateway.code
            const gatewayName = gateway.gateway_name || gateway.name || '未命名网关'

            console.log(`正在获取网关 ${gateway.id} (${gatewayName}) 的传感器数据`)
            console.log(`网关编码: ${gatewayCode}`)

            let sensors = []

            // 只使用网关编码获取传感器数据
            if (gatewayCode) {
              try {
                console.log(`使用网关编码获取传感器数据: ${gatewayCode}`)
                const response = await getSensorListByGatewayCode(gatewayCode)
                console.log(`网关编码 ${gatewayCode} 传感器API响应:`, response)

                if (response && response.code === 0 && response.data) {
                  // 处理API返回的数据结构：{ gateway: {...}, list: [...] }
                  if (response.data.list && Array.isArray(response.data.list)) {
                    sensors = response.data.list
                    // 如果有网关信息，更新网关名称
                    if (response.data.gateway) {
                      gatewayName = response.data.gateway.gateway_name || gatewayName
                    }
                  } else if (Array.isArray(response.data)) {
                    sensors = response.data
                  } else if (response.data.sensors && Array.isArray(response.data.sensors)) {
                    sensors = response.data.sensors
                  } else {
                    console.warn(`网关编码 ${gatewayCode} 返回的数据结构不符合预期:`, response.data)
                  }

                  console.log(`通过网关编码获取到 ${sensors.length} 个传感器`)
                }
              } catch (codeError) {
                console.error(`使用网关编码 ${gatewayCode} 获取传感器失败:`, codeError)
              }
            } else {
              console.warn(`网关 ${gatewayName} 没有网关编码，跳过传感器数据获取`)
            }

            // 为每个传感器添加网关信息
            if (sensors.length > 0) {
              sensors.forEach(sensor => {
                sensor.gateway = {
                  id: gateway.id,
                  name: gatewayName,
                  code: gatewayCode || gateway.id
                }
              })
              this.sensorList.push(...sensors)
              console.log(`网关 ${gatewayName} 成功添加 ${sensors.length} 个传感器`)
            } else {
              console.warn(`网关 ${gatewayName} 没有获取到传感器数据`)
            }

          } catch (error) {
            console.error(`获取网关 ${gateway.id} 的传感器数据失败:`, error)
          }
        }

        console.log(`总共获取到 ${this.sensorList.length} 个传感器`)
        console.log('传感器列表:', this.sensorList)
        console.log('按网关分组的传感器:', this.sensorsByGateway)

        // 强制更新视图
        this.$forceUpdate()
      } catch (error) {
        console.error('获取传感器数据失败:', error)
      } finally {
        this.loadingSensors = false
      }
    },

    // 刷新传感器数据
    async refreshSensorData() {
      if (this.selectedDevice) {
        console.log('手动刷新传感器数据')
        await this.fetchSensorData(this.selectedDevice)
      }
    },

    // 获取设备状态样式类
    getDeviceStatusClass(status) {
      return status === 1 ? 'online' : 'offline'
    },

    // 获取设备状态图标
    getDeviceStatusIcon(status) {
      return status === 1 ? 'fas fa-check-circle' : 'fas fa-times-circle'
    },

    // 获取设备状态文本
    getDeviceStatusText(status) {
      return status === 1 ? '在线' : '离线'
    },

    // 获取传感器图标
    getSensorIcon(sensorType) {
      const iconMap = {
        1: 'fas fa-thermometer-half', // 温度
        2: 'fas fa-tint', // 湿度
        3: 'fas fa-gauge-high', // 压力
        4: 'fas fa-compass', // 角度/风速
        5: 'fas fa-weight-hanging', // 力
        6: 'fas fa-wave-square', // 振动
        7: 'fas fa-arrows-alt-h', // 位移
        'temperature': 'fas fa-thermometer-half',
        'humidity': 'fas fa-tint',
        'pressure': 'fas fa-gauge-high',
        'force': 'fas fa-weight-hanging',
        'angle': 'fas fa-compass',
        'vibration': 'fas fa-wave-square',
        'displacement': 'fas fa-arrows-alt-h'
      }
      return iconMap[sensorType] || 'fas fa-microchip'
    },

    // 获取传感器状态样式类
    getSensorStatusClass(status) {
      return status === 1 ? 'sensor-online' : 'sensor-offline'
    },

    // 获取传感器状态图标
    getSensorStatusIcon(status) {
      return status === 1 ? 'fas fa-circle' : 'fas fa-circle'
    },

    // 获取传感器类型文本
    getSensorTypeText(sensorType) {
      const typeMap = {
        1: '温度传感器',
        2: '湿度传感器',
        3: '压力传感器',
        4: '角度传感器',
        5: '力传感器',
        6: '振动传感器',
        7: '位移传感器',
        'temperature': '温度传感器',
        'humidity': '湿度传感器',
        'pressure': '压力传感器',
        'force': '力传感器',
        'angle': '角度传感器',
        'vibration': '振动传感器',
        'displacement': '位移传感器'
      }
      return typeMap[sensorType] || '未知类型'
    },

    // 格式化传感器值
    formatSensorValue(value) {
      if (value === null || value === undefined) {
        return '--'
      }
      return typeof value === 'number' ? value.toFixed(2) : value
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return '--'
      const date = new Date(time)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }
  }
}
</script>

<style scoped>
.device-management-page {
  width: 100%;
  height: 100%;
  background: transparent;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding-top: 0;
  margin-top: 0;
}

/* 紧凑统计信息样式 */
.device-stats-compact {
  padding: 15px;
  background: rgba(0, 20, 40, 0.6);
  border-bottom: 1px solid rgba(127, 219, 255, 0.2);
  margin-bottom: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 8px;
  background: rgba(0, 30, 60, 0.4);
  border: 1px solid rgba(127, 219, 255, 0.2);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  border-color: rgba(127, 219, 255, 0.4);
  background: rgba(0, 30, 60, 0.6);
}

.stat-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: linear-gradient(135deg, #7fdbff, #0074d9);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #ffffff;
  margin-right: 10px;
  box-shadow: 0 2px 6px rgba(127, 219, 255, 0.3);
}

.stat-item.online .stat-icon {
  background: linear-gradient(135deg, #2ecc40, #27ae60);
}

.stat-item.offline .stat-icon {
  background: linear-gradient(135deg, #ff4136, #e74c3c);
}

.stat-item.sensors .stat-icon {
  background: linear-gradient(135deg, #ff851b, #f39c12);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 1.4rem;
  font-weight: 700;
  color: #7fdbff;
  text-shadow: 0 0 8px rgba(127, 219, 255, 0.5);
  margin-bottom: 2px;
  font-family: 'Orbitron', monospace;
}

.stat-item.online .stat-number {
  color: #2ecc40;
  text-shadow: 0 0 8px rgba(46, 204, 64, 0.5);
}

.stat-item.offline .stat-number {
  color: #ff4136;
  text-shadow: 0 0 8px rgba(255, 65, 54, 0.5);
}

.stat-item.sensors .stat-number {
  color: #ff851b;
  text-shadow: 0 0 8px rgba(255, 133, 27, 0.5);
}

.stat-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* 页面主体内容 */
.page-content {
  flex: 1;
  display: flex;
  overflow: hidden;
  margin-top: 0;
}

/* 左侧面板 */
.left-panel {
  width: 380px;
  background: rgba(0, 30, 60, 0.6);
  border-right: 1px solid rgba(127, 219, 255, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin-left: 20px;
  border-radius: 0 0 8px 8px;
  border-top: 1px solid rgba(127, 219, 255, 0.3);
}

.panel-header {
  padding: 12px 15px;
  border-bottom: 1px solid rgba(127, 219, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 40, 80, 0.4);
}

.panel-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #7fdbff;
  display: flex;
  align-items: center;
  gap: 6px;
}

.device-count {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(127, 219, 255, 0.1);
  padding: 3px 6px;
  border-radius: 10px;
}

/* 设备列表 */
.device-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.device-item {
  background: rgba(0, 40, 80, 0.4);
  border: 1px solid rgba(127, 219, 255, 0.2);
  border-radius: 6px;
  margin-bottom: 6px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.device-item:hover {
  border-color: rgba(127, 219, 255, 0.5);
  background: rgba(0, 40, 80, 0.6);
  transform: translateX(5px);
}

.device-item.selected {
  border-color: #7fdbff;
  background: rgba(127, 219, 255, 0.1);
  box-shadow: 0 0 10px rgba(127, 219, 255, 0.3);
}

.device-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.device-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: #ffffff;
  display: flex;
  align-items: center;
  gap: 6px;
}

.device-name i {
  color: #7fdbff;
}

.device-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
}

.info-row .label {
  color: rgba(255, 255, 255, 0.7);
}

.info-row .value {
  color: #ffffff;
  font-weight: 500;
}

/* 右侧面板 */
.right-panel {
  flex: 1;
  background: rgba(0, 20, 40, 0.4);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin-right: 20px;
  border-radius: 0 0 8px 8px;
  border-top: 1px solid rgba(127, 219, 255, 0.3);
  border-left: 1px solid rgba(127, 219, 255, 0.3);
}

.no-selection {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  padding: 40px;
}

.no-selection i {
  font-size: 4rem;
  margin-bottom: 20px;
  color: rgba(127, 219, 255, 0.3);
}

.no-selection h3 {
  margin: 0 0 10px 0;
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.8);
}

.no-selection p {
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
}

/* 设备详情 */
.device-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.detail-header {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(127, 219, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 40, 80, 0.4);
}

.detail-header h3 {
  margin: 0;
  font-size: 1.4rem;
  color: #ffffff;
  display: flex;
  align-items: center;
  gap: 10px;
}

.detail-header h3 i {
  color: #7fdbff;
}

.device-status-large {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
}

/* 设备基本信息 */
.device-basic-info {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(127, 219, 255, 0.2);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.info-card {
  background: rgba(0, 40, 80, 0.4);
  border: 1px solid rgba(127, 219, 255, 0.2);
  border-radius: 6px;
  padding: 15px;
  text-align: center;
  transition: all 0.3s ease;
}

.info-card:hover {
  border-color: rgba(127, 219, 255, 0.5);
  transform: translateY(-2px);
}

.info-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8px;
}

.info-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: #7fdbff;
}

/* 传感器部分 */
.sensor-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 15px 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h4 {
  margin: 0;
  font-size: 1.2rem;
  color: #7fdbff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.sensor-count {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(127, 219, 255, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
}



/* 传感器容器 */
.sensor-container {
  flex: 1;
  overflow-y: auto;
  padding-right: 10px;
}

/* 网关分组 */
.gateway-group {
  margin-bottom: 20px;
}

.gateway-group:last-child {
  margin-bottom: 0;
}

/* 网关头部 */
.gateway-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(0, 50, 100, 0.6);
  border: 1px solid rgba(127, 219, 255, 0.3);
  border-radius: 6px 6px 0 0;
  margin-bottom: 0;
}

.gateway-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.gateway-info i {
  font-size: 1.2rem;
  color: #7fdbff;
  background: rgba(127, 219, 255, 0.15);
  padding: 8px;
  border-radius: 50%;
  border: 1px solid rgba(127, 219, 255, 0.3);
}

.gateway-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.gateway-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: #ffffff;
}

.gateway-code {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
}

.sensor-count-badge {
  background: rgba(127, 219, 255, 0.2);
  color: #7fdbff;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid rgba(127, 219, 255, 0.3);
}

/* 传感器网格 */
.sensor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 8px;
  background: rgba(0, 30, 60, 0.3);
  border: 1px solid rgba(127, 219, 255, 0.2);
  border-top: none;
  border-radius: 0 0 6px 6px;
  padding: 8px;
}

.sensor-card {
  background: rgba(0, 40, 80, 0.4);
  border: 1px solid rgba(127, 219, 255, 0.2);
  border-radius: 4px;
  padding: 6px;
  transition: all 0.3s ease;
  height: fit-content;
}

.sensor-card:hover {
  border-color: rgba(127, 219, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(127, 219, 255, 0.1);
}

.sensor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.sensor-name {
  font-size: 0.75rem;
  font-weight: 600;
  color: #ffffff;
  display: flex;
  align-items: center;
  gap: 4px;
  line-height: 1.2;
}

.sensor-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sensor-status.sensor-online i {
  color: #00ff9d;
}

.sensor-status.sensor-offline i {
  color: #ff5555;
}

.sensor-body {
  text-align: center;
}

.sensor-type {
  font-size: 0.65rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 3px;
  line-height: 1.1;
}

.sensor-value {
  margin-bottom: 3px;
}

.sensor-value .value {
  font-size: 1rem;
  font-weight: bold;
  color: #7fdbff;
  line-height: 1.1;
}

.sensor-value .unit {
  font-size: 0.65rem;
  color: rgba(255, 255, 255, 0.7);
  margin-left: 2px;
}

.sensor-time {
  font-size: 0.6rem;
  color: rgba(255, 255, 255, 0.5);
  line-height: 1.1;
}





/* 设备状态样式 */
.device-status {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.device-status.online, .device-status-large.online {
  background: rgba(0, 255, 157, 0.2);
  color: #00ff9d;
  border: 1px solid rgba(0, 255, 157, 0.3);
}

.device-status.offline, .device-status-large.offline {
  background: rgba(255, 85, 85, 0.2);
  color: #ff5555;
  border: 1px solid rgba(255, 85, 85, 0.3);
}

/* 加载和无数据样式 */
.loading-container, .no-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: rgba(127, 219, 255, 0.6);
  text-align: center;
}

.loading-container i, .no-data-container i {
  font-size: 3rem;
  margin-bottom: 15px;
  opacity: 0.7;
}

.loading-container span, .no-data-container p {
  font-size: 1.1rem;
  margin: 0;
}

.loading-container i.fa-spin {
  animation: spin 1s linear infinite;
}

/* 滚动条样式 */
.device-list::-webkit-scrollbar,
.sensor-container::-webkit-scrollbar {
  width: 6px;
}

.device-list::-webkit-scrollbar-track,
.sensor-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.device-list::-webkit-scrollbar-thumb,
.sensor-container::-webkit-scrollbar-thumb {
  background: rgba(127, 219, 255, 0.3);
  border-radius: 3px;
}

.device-list::-webkit-scrollbar-thumb:hover,
.sensor-container::-webkit-scrollbar-thumb:hover {
  background: rgba(127, 219, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-panel {
    width: 350px;
    margin-left: 15px;
  }

  .right-panel {
    margin-right: 15px;
  }

  .info-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .sensor-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  }

  .gateway-header {
    padding: 10px 12px;
  }

  .gateway-name {
    font-size: 0.9rem;
  }

  .gateway-code {
    font-size: 0.75rem;
  }

  .sensor-count-badge {
    font-size: 0.75rem;
    padding: 3px 6px;
  }
}

@media (max-width: 900px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }

  .stat-item {
    padding: 6px;
  }

  .stat-icon {
    width: 24px;
    height: 24px;
    font-size: 10px;
    margin-right: 8px;
  }

  .stat-number {
    font-size: 1.2rem;
  }

  .stat-label {
    font-size: 0.7rem;
  }

  .device-item {
    padding: 8px;
    margin-bottom: 5px;
  }

  .device-name {
    font-size: 0.85rem;
  }

  .info-row {
    font-size: 0.75rem;
  }
}

@media (max-width: 768px) {
  .device-stats-compact {
    padding: 10px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }

  .stat-item {
    padding: 6px;
  }

  .stat-icon {
    width: 22px;
    height: 22px;
    font-size: 9px;
    margin-right: 6px;
  }

  .stat-number {
    font-size: 1.1rem;
  }

  .stat-label {
    font-size: 0.65rem;
  }

  .device-item {
    padding: 6px;
    margin-bottom: 4px;
  }

  .device-name {
    font-size: 0.8rem;
  }

  .info-row {
    font-size: 0.7rem;
  }

  .device-item-header {
    margin-bottom: 4px;
  }

  .device-info {
    gap: 3px;
  }

  .panel-header {
    padding: 10px 12px;
  }

  .panel-header h3 {
    font-size: 1rem;
  }

  .device-count {
    font-size: 0.75rem;
    padding: 2px 5px;
  }

  .page-content {
    flex-direction: column;
    margin-top: 0;
  }

  .left-panel {
    width: 100%;
    height: 250px;
    border-right: none;
    border-bottom: 1px solid rgba(127, 219, 255, 0.3);
    margin-left: 10px;
    margin-right: 10px;
    border-radius: 0 0 8px 8px;
    border-top: 1px solid rgba(127, 219, 255, 0.3);
  }

  .right-panel {
    flex: 1;
    margin-left: 10px;
    margin-right: 10px;
    border-radius: 0 0 8px 8px;
    border-top: 1px solid rgba(127, 219, 255, 0.3);
    border-left: 1px solid rgba(127, 219, 255, 0.3);
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .sensor-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    padding: 6px;
    gap: 6px;
  }

  .gateway-header {
    padding: 8px 10px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .gateway-info {
    gap: 8px;
  }

  .gateway-info i {
    font-size: 1rem;
    padding: 6px;
  }

  .gateway-name {
    font-size: 0.85rem;
  }

  .gateway-code {
    font-size: 0.7rem;
  }

  .sensor-count-badge {
    font-size: 0.7rem;
    padding: 2px 5px;
  }

  .gateway-group {
    margin-bottom: 15px;
  }

  .detail-header {
    padding: 12px 15px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .device-basic-info,
  .sensor-section {
    padding: 12px 15px;
  }
}
</style>
